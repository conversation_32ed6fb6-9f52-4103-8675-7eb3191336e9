/**
 * Enterprise Database Connection Pool Manager
 * Advanced connection pooling with monitoring, health checks, and auto-recovery
 */

import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';

export interface ConnectionPoolConfig {
  minConnections: number;
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  maxLifetime: number;
  healthCheckInterval: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface ConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingRequests: number;
  totalRequests: number;
  successfulConnections: number;
  failedConnections: number;
  averageConnectionTime: number;
  peakConnections: number;
  lastHealthCheck: Date;
  uptime: number;
}

export interface ConnectionMetrics {
  timestamp: Date;
  connectionTime: number;
  queryTime: number;
  success: boolean;
  error?: string;
}

export class DatabaseConnectionPool extends EventEmitter {
  private config: ConnectionPoolConfig;
  private stats: ConnectionPoolStats;
  private metrics: ConnectionMetrics[] = [];
  private healthCheckTimer?: NodeJS.Timeout;
  private startTime: Date;
  private isShuttingDown = false;

  constructor(config: Partial<ConnectionPoolConfig> = {}) {
    super();
    
    this.config = {
      minConnections: parseInt(process.env.DB_MIN_CONNECTIONS || '2'),
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
      idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
      maxLifetime: parseInt(process.env.DB_MAX_LIFETIME || '3600000'), // 1 hour
      healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
      retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
      ...config,
    };

    this.startTime = new Date();
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      totalRequests: 0,
      successfulConnections: 0,
      failedConnections: 0,
      averageConnectionTime: 0,
      peakConnections: 0,
      lastHealthCheck: new Date(),
      uptime: 0,
    };

    this.startHealthChecks();
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(async () => {
      if (!this.isShuttingDown) {
        await this.performHealthCheck();
      }
    }, this.config.healthCheckInterval);
  }

  /**
   * Perform database health check
   */
  private async performHealthCheck(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Import prisma here to avoid circular dependencies
      const { prisma } = await import('../prisma');
      
      await prisma.$queryRaw`SELECT 1 as health_check`;
      
      this.stats.lastHealthCheck = new Date();
      this.emit('healthCheck', { success: true, latency: Date.now() - startTime });
      
    } catch (error) {
      this.emit('healthCheck', { 
        success: false, 
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      console.error('Database health check failed:', error);
    }
  }

  /**
   * Record connection metrics
   */
  recordMetrics(metrics: Omit<ConnectionMetrics, 'timestamp'>): void {
    const metric: ConnectionMetrics = {
      ...metrics,
      timestamp: new Date(),
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics (last 1000 entries)
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Update statistics
    this.updateStats(metric);
  }

  /**
   * Update connection statistics
   */
  private updateStats(metric: ConnectionMetrics): void {
    this.stats.totalRequests++;
    
    if (metric.success) {
      this.stats.successfulConnections++;
    } else {
      this.stats.failedConnections++;
    }

    // Calculate average connection time
    const recentMetrics = this.metrics.slice(-100);
    const totalTime = recentMetrics.reduce((sum, m) => sum + m.connectionTime, 0);
    this.stats.averageConnectionTime = totalTime / recentMetrics.length;

    // Update uptime
    this.stats.uptime = Date.now() - this.startTime.getTime();
  }

  /**
   * Get current pool statistics
   */
  getStats(): ConnectionPoolStats {
    return { ...this.stats };
  }

  /**
   * Get recent metrics
   */
  getMetrics(limit: number = 100): ConnectionMetrics[] {
    return this.metrics.slice(-limit);
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.getStats();

    if (stats.averageConnectionTime > 1000) {
      recommendations.push('High average connection time detected. Consider optimizing database queries or increasing connection pool size.');
    }

    if (stats.failedConnections > stats.successfulConnections * 0.1) {
      recommendations.push('High connection failure rate detected. Check database connectivity and configuration.');
    }

    if (stats.peakConnections > this.config.maxConnections * 0.8) {
      recommendations.push('Connection pool utilization is high. Consider increasing maxConnections.');
    }

    const recentFailures = this.metrics
      .slice(-50)
      .filter(m => !m.success).length;
    
    if (recentFailures > 5) {
      recommendations.push('Recent connection failures detected. Monitor database health and network connectivity.');
    }

    return recommendations;
  }

  /**
   * Optimize pool configuration based on current metrics
   */
  optimizeConfiguration(): Partial<ConnectionPoolConfig> {
    const stats = this.getStats();
    const recommendations: Partial<ConnectionPoolConfig> = {};

    // Adjust max connections based on usage patterns
    if (stats.peakConnections > this.config.maxConnections * 0.9) {
      recommendations.maxConnections = Math.min(
        this.config.maxConnections + 5,
        50 // Cap at 50 connections
      );
    }

    // Adjust timeouts based on performance
    if (stats.averageConnectionTime > this.config.connectionTimeout * 0.8) {
      recommendations.connectionTimeout = Math.min(
        this.config.connectionTimeout + 2000,
        30000 // Cap at 30 seconds
      );
    }

    return recommendations;
  }

  /**
   * Reset metrics and statistics
   */
  reset(): void {
    this.metrics = [];
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      totalRequests: 0,
      successfulConnections: 0,
      failedConnections: 0,
      averageConnectionTime: 0,
      peakConnections: 0,
      lastHealthCheck: new Date(),
      uptime: 0,
    };
    this.startTime = new Date();
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.emit('shutdown');
    console.log('Database connection pool shutdown completed');
  }
}

// Singleton instance
export const connectionPool = new DatabaseConnectionPool();

// Export configuration for external use
export const getConnectionPoolConfig = (): ConnectionPoolConfig => ({
  minConnections: parseInt(process.env.DB_MIN_CONNECTIONS || '2'),
  maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
  connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
  idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
  maxLifetime: parseInt(process.env.DB_MAX_LIFETIME || '3600000'),
  healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'),
  retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
  retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
});
