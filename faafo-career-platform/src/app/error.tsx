'use client';

import { useEffect } from 'react';

// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <div className="max-w-md w-full bg-card rounded-lg shadow-lg p-8 text-center border">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>

          <h1 className="text-2xl font-bold text-foreground mb-2">
            Something went wrong!
          </h1>

          <p className="text-muted-foreground mb-4">
            We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details className="text-left bg-muted p-4 rounded-md mb-4">
              <summary className="cursor-pointer font-medium text-sm">
                Error Details (Development Only)
              </summary>
              <pre className="mt-2 text-xs overflow-auto">
                {error.message}
                {error.stack && (
                  <>
                    {'\n\nStack Trace:\n'}
                    {error.stack}
                  </>
                )}
              </pre>
            </details>
          )}
        </div>

        <div className="space-y-3">
          <Button onClick={reset} className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>

          <Link href="/" className="inline-flex items-center justify-center w-full min-h-[44px] px-4 py-2 border border-input bg-background rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
            <Home className="h-4 w-4 mr-2" />
            Go to Home
          </Link>
        </div>

        <div className="mt-6 text-sm text-muted-foreground">
          <p>
            Error ID: {error.digest || 'Unknown'}
          </p>
        </div>
      </div>
    </div>
  );
}
