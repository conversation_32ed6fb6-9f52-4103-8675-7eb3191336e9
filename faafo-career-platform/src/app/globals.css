@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 0 0% 14.5%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.5%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.5%;
  --primary: 0 0% 20.5%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 97%;
  --secondary-foreground: 0 0% 20.5%;
  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 55.6%;
  --accent: 0 0% 97%;
  --accent-foreground: 0 0% 20.5%;
  --destructive: 27 24.5% 57.7%;
  --border: 0 0% 92.2%;
  --input: 0 0% 92.2%;
  --ring: 0 0% 70.8%;
  --chart-1: 41 22.2% 64.6%;
  --chart-2: 184 11.8% 60%;
  --chart-3: 227 7% 39.8%;
  --chart-4: 84 18.9% 82.8%;
  --chart-5: 70 18.8% 76.9%;
  --sidebar: 0 0% 98.5%;
  --sidebar-foreground: 0 0% 14.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 97%;
  --sidebar-accent-foreground: 0 0% 20.5%;
  --sidebar-border: 0 0% 92.2%;
  --sidebar-ring: 0 0% 70.8%;
}

.dark {
  --background: 0 0% 14.5%;
  --foreground: 0 0% 98.5%;
  --card: 0 0% 20.5%;
  --card-foreground: 0 0% 98.5%;
  --popover: 0 0% 20.5%;
  --popover-foreground: 0 0% 98.5%;
  --primary: 0 0% 92.2%;
  --primary-foreground: 0 0% 20.5%;
  --secondary: 0 0% 26.9%;
  --secondary-foreground: 0 0% 98.5%;
  --muted: 0 0% 26.9%;
  --muted-foreground: 0 0% 70.8%;
  --accent: 0 0% 26.9%;
  --accent-foreground: 0 0% 98.5%;
  --destructive: 22 19.1% 70.4%;
  --border: 0 0% 100% / 10%;
  --input: 0 0% 100% / 15%;
  --ring: 0 0% 55.6%;
  --chart-1: 264 24.3% 48.8%;
  --chart-2: 162 17% 69.6%;
  --chart-3: 70 18.8% 76.9%;
  --chart-4: 303 26.5% 62.7%;
  --chart-5: 16 24.6% 64.5%;
  --sidebar: 0 0% 20.5%;
  --sidebar-foreground: 0 0% 98.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 26.9%;
  --sidebar-accent-foreground: 0 0% 98.5%;
  --sidebar-border: 0 0% 100% / 10%;
  --sidebar-ring: 0 0% 55.6%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    /* background-color: hsl(var(--background)) !important; */
    /* color: hsl(var(--foreground)) !important; */
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  /* Add a clear focus indicator */
  :focus-visible {
    outline: 3px solid hsl(41 22.2% 64.6%);
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Force dropdown menus to appear above everything */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }

  [role="menu"] {
    z-index: 9999 !important;
  }

  /* Ensure minimum touch target sizes for accessibility (44x44px minimum) */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  a[href],
  [tabindex]:not([tabindex="-1"]),
  select,
  textarea,
  input:not([type="hidden"]) {
    min-height: 44px;
    min-width: 44px;
  }

  /* Exception for inline links within text */
  p a,
  span a,
  li a:not(.button):not([role="button"]) {
    min-height: auto;
    min-width: auto;
    display: inline;
  }

  /* Ensure buttons have adequate padding */
  button:not(.no-min-size),
  [role="button"]:not(.no-min-size) {
    padding: 8px 16px;
  }

  /* Improve focus indicators for better accessibility */
  button:focus-visible,
  [role="button"]:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible,
  a:focus-visible {
    outline: 3px solid hsl(41 22.2% 64.6%);
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Ensure adequate spacing between interactive elements */
  button + button,
  [role="button"] + [role="button"],
  a + a {
    margin-left: 8px;
  }

  /* Skip link styling */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
    border-radius: 4px;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Improve form accessibility */
  label {
    font-weight: 500;
    margin-bottom: 4px;
    display: block;
  }

  /* Error message styling */
  [role="alert"],
  .error-message {
    color: hsl(var(--destructive));
    font-weight: 500;
    margin-top: 4px;
  }

  /* Loading state accessibility */
  [aria-busy="true"] {
    cursor: wait;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :focus-visible {
      outline: 4px solid;
      outline-offset: 2px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Enhanced Performance Optimizations */

  /* Optimize font loading */
  @font-face {
    font-display: swap;
  }

  /* Optimize image loading */
  img {
    loading: lazy;
    decoding: async;
  }

  /* Critical images should load immediately */
  img[data-priority="high"] {
    loading: eager;
  }

  /* Optimize animations for performance */
  @media (prefers-reduced-motion: no-preference) {
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }

    .animate-slide-up {
      animation: slideUp 0.4s ease-out;
    }

    .animate-scale-in {
      animation: scaleIn 0.2s ease-out;
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Enhanced Loading States */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  /* Enhanced Error States */
  .error-boundary {
    padding: 2rem;
    text-align: center;
    border: 2px dashed hsl(var(--destructive));
    border-radius: 8px;
    background: hsl(var(--destructive) / 0.05);
  }

  .error-message {
    color: hsl(var(--destructive));
    font-weight: 500;
    margin-bottom: 1rem;
  }

  /* Enhanced Form Validation */
  .form-field-error {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2);
  }

  .form-field-success {
    border-color: hsl(var(--success));
    box-shadow: 0 0 0 2px hsl(var(--success) / 0.2);
  }

  /* Enhanced Security Indicators */
  .security-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .security-indicator.secure {
    background: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
  }

  .security-indicator.warning {
    background: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
  }

  .security-indicator.danger {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
  }

  /* Enhanced Print Styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    a[href]:after {
      content: " (" attr(href) ")";
      font-size: 0.8em;
      color: #666;
    }

    .page-break {
      page-break-before: always;
    }
  }

  /* Enhanced Dark Mode Support */
  @media (prefers-color-scheme: dark) {
    :root {
      --background: 222.2 84% 4.9%;
      --foreground: 210 40% 98%;
      --card: 222.2 84% 4.9%;
      --card-foreground: 210 40% 98%;
      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;
      --primary: 210 40% 98%;
      --primary-foreground: 222.2 84% 4.9%;
      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;
      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;
      --accent: 217.2 32.6% 17.5%;
      --accent-foreground: 210 40% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;
      --border: 217.2 32.6% 17.5%;
      --input: 217.2 32.6% 17.5%;
      --ring: 212.7 26.8% 83.9%;
    }
  }

  /* Enhanced Accessibility Classes */

  /* High Contrast Mode */
  .high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 80%;
    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 40%;
    --input: 0 0% 20%;
    --ring: 0 0% 60%;
  }

  .high-contrast * {
    border-color: hsl(var(--border)) !important;
  }

  .high-contrast button,
  .high-contrast [role="button"] {
    border: 2px solid hsl(var(--border)) !important;
  }

  /* Large Text Mode */
  .large-text {
    font-size: 120% !important;
  }

  .large-text h1 { font-size: 3rem !important; }
  .large-text h2 { font-size: 2.5rem !important; }
  .large-text h3 { font-size: 2rem !important; }
  .large-text h4 { font-size: 1.75rem !important; }
  .large-text h5 { font-size: 1.5rem !important; }
  .large-text h6 { font-size: 1.25rem !important; }

  .large-text button,
  .large-text [role="button"] {
    font-size: 1.1rem !important;
    padding: 12px 20px !important;
  }

  /* Reduced Motion Mode */
  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Enhanced Focus Indicators */
  .enhanced-focus *:focus-visible {
    outline: 4px solid hsl(var(--ring)) !important;
    outline-offset: 4px !important;
    border-radius: 4px !important;
  }

  .enhanced-focus button:focus-visible,
  .enhanced-focus [role="button"]:focus-visible {
    box-shadow: 0 0 0 4px hsl(var(--ring)) !important;
  }

  /* Screen Reader Optimizations */
  .screen-reader-optimized .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0.25rem !important;
    margin: 0.25rem !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    background: hsl(var(--muted));
    border: 1px solid hsl(var(--border));
    border-radius: 4px;
  }

  .screen-reader-optimized [aria-hidden="true"] {
    display: none !important;
  }

  /* Keyboard Navigation Enhancements */
  .keyboard-navigation {
    --focus-ring-width: 3px;
    --focus-ring-offset: 2px;
  }

  .keyboard-navigation *:focus {
    outline: var(--focus-ring-width) solid hsl(var(--ring));
    outline-offset: var(--focus-ring-offset);
  }

  .keyboard-navigation button:focus,
  .keyboard-navigation [role="button"]:focus,
  .keyboard-navigation a:focus,
  .keyboard-navigation input:focus,
  .keyboard-navigation select:focus,
  .keyboard-navigation textarea:focus {
    z-index: 10;
    position: relative;
  }

  /* Skip Links Enhancement */
  .keyboard-navigation .skip-link:focus {
    position: fixed !important;
    top: 1rem !important;
    left: 1rem !important;
    z-index: 9999 !important;
    padding: 1rem !important;
    background: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    text-decoration: none !important;
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  }

  /* Loading State Accessibility */
  [aria-busy="true"] {
    position: relative;
  }

  [aria-busy="true"]::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      hsl(var(--muted)) 10px,
      hsl(var(--muted)) 20px
    );
    opacity: 0.1;
    pointer-events: none;
  }

  /* Error State Accessibility */
  [role="alert"] {
    padding: 1rem;
    border: 2px solid hsl(var(--destructive));
    border-radius: 0.5rem;
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive-foreground));
  }

  [aria-invalid="true"] {
    border-color: hsl(var(--destructive)) !important;
    box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2) !important;
  }

  /* Success State Accessibility */
  [aria-invalid="false"] {
    border-color: hsl(142 76% 36%) !important;
    box-shadow: 0 0 0 2px hsl(142 76% 36% / 0.2) !important;
  }
}
