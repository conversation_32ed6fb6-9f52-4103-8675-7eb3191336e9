// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <div className="max-w-md w-full bg-card rounded-lg shadow-lg p-8 text-center border">
        <div className="mb-6">
          <h1 className="text-6xl font-bold text-primary mb-2">
            404
          </h1>
          <h2 className="text-xl font-semibold text-foreground mb-4">
            Page Not Found
          </h2>
          <p className="text-muted-foreground">
            Sorry, we couldn't find the page you're looking for. The page may have been moved, deleted, or you may have entered an incorrect URL.
          </p>
        </div>

        <div className="space-y-3">
          <a href="/" className="inline-flex items-center justify-center w-full min-h-[44px] px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
            🏠 Return to Home
          </a>

          <a href="/assessment" className="inline-flex items-center justify-center w-full min-h-[44px] px-4 py-2 border border-input bg-background rounded-md hover:bg-accent hover:text-accent-foreground transition-colors">
            📝 Take Assessment
          </a>

          <a href="/career-paths" className="inline-flex items-center justify-center w-full min-h-[44px] px-4 py-2 hover:bg-accent hover:text-accent-foreground rounded-md transition-colors">
            🔍 Explore Career Paths
          </a>
        </div>

        <div className="mt-6 text-sm text-muted-foreground">
          <p className="mb-2">
            If you believe this is an error, please contact support.
          </p>
          <a
            href="/contact"
            className="text-primary hover:underline min-h-[44px] inline-flex items-center justify-center py-2 px-4 rounded hover:bg-primary/10"
          >
            Contact Support
          </a>
        </div>
      </div>
    </div>
  );
}
